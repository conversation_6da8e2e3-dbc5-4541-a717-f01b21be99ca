import { useCallback, useEffect, useRef } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { TeamManager, TokenManager } from '@/libs/storage'
import { UploadTask } from '@app/shared/types/upload-task.types.js'
import useRequest from './useRequest'
import { FolderUploadParams, SelectFilesParams } from '@app/shared/types/ipc/upload-task'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { toast } from 'react-toastify'

/**
 * 上传任务管理 Hook
 */

type UploadEventCallback = (payload: UploadTask.CompleteEvent) => void

export function useUploadTasks() {
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  const callbacksRef = useRef<Map<string, Set<UploadEventCallback>>>(new Map())

  const subscribe = (event: string, callback: UploadEventCallback) => {
    const set = callbacksRef.current.get(event) || new Set()
    set.add(callback)
    callbacksRef.current.set(event, set)
  }

  const unsubscribe = (event: string, callback: UploadEventCallback) => {
    const set = callbacksRef.current.get(event)
    if (!set) return
    set.delete(callback)
  }

  // 获取用户上传任务
  const { data: tasks = [], isLoading, refetch } = useQuery({
    queryKey: ['uploadTasks', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return []
      return window.uploadTask.getUserTasks({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 5000
  })

  // 获取上传统计
  const { data: stats } = useQuery({
    queryKey: ['uploadTaskStats', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return null
      return window.uploadTask.getTaskStats({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 15000
  })

  // 获取队列状态
  const { data: queueStatus } = useQuery({
    queryKey: ['uploadQueueStatus'],
    queryFn: () => window.uploadTask.getQueueStatus(),
    refetchInterval: 5000
  })

  // 创建上传任务
  const createTaskMutation = useMutation({
    mutationFn: async (params: UploadTask.CreateParams) => {
      return window.uploadTask.create(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.UPLOADING_TASKS] })
    }
  })

  // 开始上传
  const startUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.startUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.UPLOADING_TASKS] })
    }
  })

  // 暂停上传
  const pauseUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      console.log(`[useUploadTasks] 开始暂停任务 ${taskId}`)
      const result = await window.uploadTask.pauseUpload({ id: taskId })
      console.log(`[useUploadTasks] 暂停任务 ${taskId} 完成`)
      return result
    },
    onSuccess: () => {
      // 成功后立即刷新数据，确保状态同步
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    },
    onError: err => {
      console.error('[useUploadTasks] 暂停上传失败:', err)
    }
  })

  // 恢复上传
  const resumeUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      console.log(`[useUploadTasks] 开始恢复任务 ${taskId}`)
      const result = await window.uploadTask.resumeUpload({ id: taskId })
      console.log(`[useUploadTasks] 恢复任务 ${taskId} 完成`)
      return result
    },
    onSuccess: () => {
      // 成功后立即刷新数据，确保状态同步
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    },
    onError: err => {
      console.error('[useUploadTasks] 恢复上传失败:', err)
    }
  })

  // 取消上传
  const cancelUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.cancelUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 重试上传
  const retryUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.retryUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 批量操作
  const batchOperationMutation = useMutation({
    mutationFn: async (params: UploadTask.BatchParams) => {
      return window.uploadTask.batchOperation(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.delete(taskId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 清理已完成任务
  const cleanupCompletedMutation = useMutation({
    mutationFn: async () => {
      if (!currentUser) return 0
      return window.uploadTask.cleanupCompleted({
        uid: String(currentUser),
        teamId: currentTeam,
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 从路径上传文件
  const uploadFromPathMutation = useMutation({
    mutationFn: async (params: {
      taskIds: number[],
      maxSize?: number
    }) => {
      return window.uploadTask.uploadFromPath(params)
    },
    onSuccess: result => {
      if (!result.success) {
        toast.warn(result.error)
      }
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 上传文件夹
  const uploadFolderMutation = useMutation({
    mutationFn: async (params: FolderUploadParams) => {
      return window.uploadTask.uploadFolder(params)
    },
    onSuccess: result => {
      if (!result.success) {
        toast.warn(result.error)
      }
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  const selectFilesMutation = useRequest(async(
    data: SelectFilesParams
  ) => {
    return await window.uploadTask.selectFiles({
      multiple: data.multiple,
      filters: data.filters,
      folder: data.folder
    })
  }, {
    showSuccessToast: false
  })

  // 监听上传进度事件
  useEffect(() => {
    const handleBatchProgressEvent = (_event: any, progressEvents: UploadTask.ProgressEvent[]) => {
      console.log(`[useUploadTasks] 接收到批量进度事件: ${progressEvents.length} 个任务`)

      // 批量更新任务进度
      queryClient.setQueryData(['uploadTasks', currentUser, currentTeam], (oldTasks: UploadTask.IUploadTask[] | undefined) => {
        if (!oldTasks) return oldTasks

        // 创建进度更新映射
        const progressMap = new Map<number, number>()
        progressEvents.forEach(event => {
          progressMap.set(event.task_id, event.progress)
          console.log(`[useUploadTasks] 任务${event.task_id}, 进度${(event.progress * 100).toFixed(1)}%`)
        })

        return oldTasks.map(task => {
          const newProgress = progressMap.get(task.id)
          if (newProgress !== undefined) {
            const shouldUpdateStatus = task.status === UploadTask.Status.PENDING && newProgress > 0

            if (shouldUpdateStatus) {
              console.log(`[useUploadTasks] 任务${task.id} 状态从 PENDING 更新为 UPLOADING (进度: ${(newProgress * 100).toFixed(1)}%)`)
            }

            return {
              ...task,
              progress: newProgress,
              status: shouldUpdateStatus ? UploadTask.Status.UPLOADING : task.status,
              updated_at: Date.now()
            }
          }
          return task
        })
      })

      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }

    window.electronAPI?.ipcRenderer.on('batch-upload-progress', handleBatchProgressEvent)

    return () => {
      window.electronAPI?.ipcRenderer.removeListener('batch-upload-progress', handleBatchProgressEvent)
    }
  }, [currentUser, currentTeam])

  // 监听批次完成事件
  useEffect(() => {
    const handleBatchComplete = (_event: any, payload: UploadTask.CompleteEvent) => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })

      console.log('[useUploadTasks] 批次完成:', payload)

      callbacksRef.current.get('batch-upload-complete')?.forEach(cb => cb(payload))
    }

    window.electronAPI?.ipcRenderer.on('batch-upload-complete', handleBatchComplete)
    return () => {
      window.electronAPI?.ipcRenderer.removeListener('batch-upload-complete', handleBatchComplete)
    }
  }, [])

  const getTasksByStatus = useCallback((status: UploadTask.Status) => {
    return tasks.filter(task => task.status === status)
  }, [tasks])

  const getPendingTasks = useCallback(() => getTasksByStatus(UploadTask.Status.PENDING), [getTasksByStatus])
  const getUploadingTasks = useCallback(() => getTasksByStatus(UploadTask.Status.UPLOADING), [getTasksByStatus])
  const getPausedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.PAUSED), [getTasksByStatus])
  const getCompletedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.COMPLETED), [getTasksByStatus])
  const getFailedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.FAILED), [getTasksByStatus])

  return {
    // 数据
    tasks,
    stats,
    queueStatus,
    isLoading,

    // 查询方法
    refetch,
    getTasksByStatus,
    getPendingTasks,
    getUploadingTasks,
    getPausedTasks,
    getCompletedTasks,
    getFailedTasks,

    // 操作方法
    selectFiles: selectFilesMutation.mutateAsync,
    createTask: createTaskMutation.mutateAsync,
    startUpload: startUploadMutation.mutateAsync,
    pauseUpload: pauseUploadMutation.mutateAsync,
    resumeUpload: resumeUploadMutation.mutateAsync,
    cancelUpload: cancelUploadMutation.mutateAsync,
    retryUpload: retryUploadMutation.mutateAsync,
    batchOperation: batchOperationMutation.mutateAsync,
    deleteTask: deleteTaskMutation.mutateAsync,
    cleanupCompleted: cleanupCompletedMutation.mutateAsync,
    uploadFromPath: uploadFromPathMutation.mutateAsync,
    uploadFolder: uploadFolderMutation.mutateAsync,

    subscribe,
    unsubscribe
  }
}
