import React, { ReactNode, useEffect } from 'react'
import { EditorContext } from './context'
import { useOverlays } from './useOverlays'
import { useVideoPlayer } from './useVideoPlayer'
import { useAspectRatio } from './useAspectRatio'
import { useCompositionDuration } from './useCompositionDuration'
import { useHistory } from './useHistory'
import { cleanupPlugins, initializePlugins } from '@/modules/video-editor/resource-plugin-system'

export const EditorProvider: React.FC<{
  scriptId: string
  projectId: string
  scriptName?: string
  children: ReactNode
}> = ({ children, scriptId, projectId, scriptName }) => {
  const overlaysHook = useOverlays()

  const aspectRatioHook = useAspectRatio('9:16')

  const { tracks, setTracksDirectly } = overlaysHook

  const compositionDuration = useCompositionDuration(tracks)
  const videoPlayerHook = useVideoPlayer()

  const historyHook = useHistory(tracks, setTracksDirectly)

  useEffect(() => {
    initializePlugins()

    return () => {
      cleanupPlugins()
    }
  }, [])

  return (
    <EditorContext.Provider
      value={{
        scriptId,
        projectId,
        scriptName,
        ...overlaysHook,
        ...aspectRatioHook,
        ...historyHook,
        ...compositionDuration,
        videoPlayer: videoPlayerHook,
        history: historyHook,
      }}
    >
      {children}
    </EditorContext.Provider>
  )
}

