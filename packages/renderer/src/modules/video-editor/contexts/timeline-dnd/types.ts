import { Overlay } from '@clipnest/remotion-shared/types'
import { GhostElement } from '@/modules/video-editor/types'
import {
  CloudResourceTypes,
  MaterialResource,
  PasterResource,
  ResourceSource,
  SoundResource,
  StyledTextResource
} from '@/types/resources'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'

export class OverlaysAdjustment extends Map<
  /**
   * Overlay ID
   */
  number,
  {
    /**
     * 起始帧的变化量
     */
    fromFrameShift?: number
    /**
     * 持续时长的变化量
     */
    durationShift?: number
    /**
     * 目标分镜序号
     */
    targetStoryboardIndex?: number
  }
> {

  public apply(another: OverlaysAdjustment) {
    another.forEach((value, key) => {
      this.set(key, value)
    })
  }
}

export interface DraggableState {
  /**
   * 是否允许拖拽 (无论为何种 `DragAction`)
   */
  draggable: boolean

  /**
   * 拖拽导致的其他 Overlay 的变动
   */
  overlaysAdjust?: OverlaysAdjustment

  /**
   * 拖拽过后, 目标 Overlay 的起始帧
   */
  adjustedStartFrame?: number

  /**
   * 拖拽过后, 目标 Overlay 的总时长
   */
  adjustedDuration?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的轨道序号
   */
  adjustedRow?: number

  /**
   * 处理调整长度时，对于视频/音频等有时间信息的 Overlay, 需要通过去片尾的方式实现时长缩短
   */
  trimEndRatio?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的分镜序号
   */
  targetStoryboardIndex?: number
}

type DragAction = 'move' | 'resize-end'

export interface OverlayDragInfo {
  overlay: Overlay

  initialFrom: number
  initialDurationInFrames: number
  initialRow: number

  action?: DragAction

  landingPoint?: GhostElement
  draggableState?: DraggableState

  currentFrom?: number
  currentDuration?: number
  currentRow?: number
}

export type DroppableResource =
  | { resourceType: CloudResourceTypes.MATERIAL, data: MaterialResource.Media & { materialType: ResourceSource } }
  | { resourceType: CloudResourceTypes.PASTER, data: PasterResource.Paster }
  | { resourceType: CloudResourceTypes.PASTER, data: PasterResource.PasterLocal & { materialType: ResourceSource } }
  | { resourceType: CloudResourceTypes.SOUND, data: SoundResource.Sound }
  | { resourceType: CloudResourceTypes.SOUND, data: SoundResource.SoundLocal & { materialType: ResourceSource } }
  | { resourceType: CloudResourceTypes.STYLED_TEXT, data?: StyledTextResource.StyledText }

export type ResourceMeta = {
  type: ResourceCacheType,
  url: string,
  filename?: string,
  customExt?: string
}
