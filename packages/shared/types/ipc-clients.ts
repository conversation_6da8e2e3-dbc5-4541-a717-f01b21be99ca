import { EditorIPCClient } from './ipc/editor.js'
import { MixcutIPCClient } from './ipc/mixcut.js'
import { WindowIPCClient } from './ipc/window.js'
import { ResourceIPCClient } from './ipc/resource.js'
import { FileUploaderIPCClient } from './ipc/file-uploader.js'
import { BaseInfoIPCClient } from './ipc/base-info.js'
import { AutoUpdaterIPCClient } from './ipc/auto-updater.js'
import { UploadTaskIPCClient } from './ipc/upload-task.js'
import { FileDownloaderIPCClient } from './ipc/file-downloader.js'

/**
 * Electron API 类型定义
 */
interface ElectronAPI {
  ipcRenderer: {
    on: (channel: string, callback: (...args: any[]) => void) => void
    removeListener: (channel: string, callback: (...args: any[]) => void) => void
    removeAllListeners: (channel: string) => void
  }
}

/**
 * IPC客户端类型定义
 */
export type IPCClients = {
  editor: EditorIPCClient

  mixcut: MixcutIPCClient

  windowManager: WindowIPCClient

  resource: ResourceIPCClient

  fileUploader: FileUploaderIPCClient

  fileDownloader: FileDownloaderIPCClient

  baseInfo: BaseInfoIPCClient

  autoUpdater: AutoUpdaterIPCClient

  electronAPI: ElectronAPI

  //#region ~ Crudable clients
  uploadTask: UploadTaskIPCClient
  //#endregion
}

export default IPCClients
